
Important Behavior Changes
--------------------------

 - Cloning subsystem is completely removed, replaced with
   QNodeCreationChange and QNodeDestroyedChange types.
 - QSceneChange and subclasses overhauled
 - QNodeIds are now passed and accepted in QVectors, not QLists.
 - API cleaned up and made more consistent
 - Shutdown crashes / races fixed
 - Qt3DCore, Qt3DRender, Qt3DInput and Qt3DLogic are now stable
 - New library Qt3DExtras is still unstable (see below)

Render
------

 - QRenderSettings used to specify frame graph, picking settings
   and renderer update policy.
 - Texture loading reimplemented. Supports DDS files containing
   compressed formats.
 - Added support for clearing multiple buffers.
 - Added support for separate blend modes for RGB and Alpha.
 - Fixed support for base instance argument when using instanced
   rendering.
 - Support for compute shaders.
 - QObjectPicker component high level convenience class for
   ray-cast based picking.

Input
-----

 - Added GenericDevice.
 - Improved API around buttons used as input for axes.
 - Button controlled axes now support linear ramp up/down
   curves rather than being binary valued.

Logic
-----

 - Renamed QLogicComponent to QFrameAction

Scene3D
-------

 - Provides integration point to embed Qt 3D into a Qt Quick 2
   scene.

Extras
------

 - Added unstable Qt3DExtras library to hold higher level sets
   of elements. This library will change in the future but will
   offer similar facilities.
 - To keep Render and Input aspects generic, the Extras lib
   provides a location to keep experimental higher level utilities
   such as materials and geometries. It is perfectly possible to
   use Qt 3D without Qt3DExtras but it can give you a quick start
   if you do not wish to write your own materials etc.
 - Provides some default geometries such as QSphereMesh,
   QCubeMesh, QTorusMesh etc.
 - Provides some default materials such as QPhongMaterial,
   QDiffuseMapMaterial etc. along with their associated effects.
 - Provides some example window integration points.
 - Provides some example camera controllers.
