Qt 5.8 introduces many new features and improvements as well as bugfixes
over the 5.7.x series. For more details, refer to the online documentation
included in this distribution. The documentation is also available online:

  http://doc.qt.io/qt-5/index.html

The Qt version 5.8 series is binary compatible with the 5.7.x series.
Applications compiled for 5.7 will continue to run with 5.8.

Some of the changes listed in this file include issue tracking numbers
corresponding to tasks in the Qt Bug Tracker:

  https://bugreports.qt.io/

Each of these identifiers can be entered in the bug tracker to obtain more
information about a particular change.

General
-------

 - Greatly improved test coverage
 - Added more documentation
 - Fixed large number of bugs across all of Qt 3D

Render
------

 - Texture system reworked to improve sharing of textures on the backend
   and to minimize uploads to the GPU.
 - Do not mirror texture coordinates or textures by default.
 - Uniform handling system overhauled to remove usage of QVariant on the
   backend.
 - QRenderCapture framegraph node added to allow read back of the write
   framebuffer to a QImage.
 - Improved parallelisation of several job types.
 - Improved parallelisation of OpenGL submission thread with building the
   next frame.
 - QObjectPicker is now also able to pick back facing triangles.

Input
-----

 - Added QAxisAccumulator to allow tracking and integration of QAxis values
   by treating the axis values as velocity or acceleration values.
 - Added concept of proxy devices to allow new device plugins to easily
   enumerate axes and buttons.

Extras
------

 - Various fixes to the geometry attributes
