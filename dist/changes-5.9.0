Qt 5.9 introduces many new features and improvements as well as bugfixes
over the 5.8.x series. For more details, refer to the online documentation
included in this distribution. The documentation is also available online:

http://doc.qt.io/qt-5/index.html

The Qt version 5.9 series is binary compatible with the 5.8.x series.
Applications compiled for 5.8 will continue to run with 5.9.

Some of the changes listed in this file include issue tracking numbers
corresponding to tasks in the Qt Bug Tracker:

https://bugreports.qt.io/

Each of these identifiers can be entered in the bug tracker to obtain more
information about a particular change.

Third-Party Code
----------------

- Updated assimp 3rdparty library to version 3.3.1

Important Changes
-----------------

 - Added zooming with the mouse scroll wheel to
   QFirstPersonCameraController and QOrbitCameraController.
 - Updated assimp scene importer to load simple keyframe and morphing animations.
 - New Animation module supporting key framed animations.
 - New Scene2D module supporting rendering Qt Quick within Qt 3D scenes and
   interacting with them.
 - Level of details support.
 - 2D distance field and 3D geometric text support.
 - Physics Based Rendering support.

Render
------

 - Added new QBufferCapture frame graph node to support QBuffer readback.
 - Added new QMemoryBarrier frame graph node.
 - Added EnvironmentLight type for environment mapping. Works well with PBR materials
   in Qt3DExtras to give realistic lighting and specular reflections.
 - Added support for level of detail with the QLevelOfDetail component and helpers.
   Allows switching based upon distance from camera or projected screen size.
 - Lots of performance and stability fixes.

Scene2D
-------

 - Added new Scene2D module, which allows rendering Qt Quick content to Qt 3D
   texture and using it as part of a 3D scene.

Animation
---------

 - Added technology preview of new Animation module.
 - Added new animation classes to support loading animations using assimp scene loader.
 - Added support for defining key framed animations or loading them from file.
 - Added an animation export script for Blender
 - Added classes to play back simple key frame animations and apply the animation to
   arbitrary QObjects or QNodes.
 - Added classes to support play back and blending of key frame animations. This allows
   dynamically combining libraries of animation clips at runtime.

Extras
------

 - Added support for Physically Based Rendering with QMetalRough and QTexturedMetalRough
   materials.
 - Added new QText2Entity for distance field based 2D planar text in 3D scenes.
 - Added QExtrudedTextMesh and QExtrudedTextGeometry classes to support 3D text rendering.
 - Added new QMorphPhongMaterial to support mesh morphing and phong lighting model.
 - Added new QTextureMaterial to support simple unlit textured geometry.
 - Improved API consistency between QML and C++

Examples
--------

 - Added new simple custom material and advanced custom material examples.
 - Modified multiviewport example rendered content.
 - Added new Scene2D example to showcase the new feature.
