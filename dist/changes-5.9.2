Qt 5.9.2 is a bug-fix release. It maintains both forward and backward
compatibility (source and binary) with Qt 5.9.0.

For more details, refer to the online documentation included in this
distribution. The documentation is also available online:

http://doc.qt.io/qt-5/index.html

The Qt version 5.9 series is binary compatible with the 5.8.x series.
Applications compiled for 5.8 will continue to run with 5.9.

Some of the changes listed in this file include issue tracking numbers
corresponding to tasks in the Qt Bug Tracker:

https://bugreports.qt.io/

Each of these identifiers can be entered in the bug tracker to obtain more
information about a particular change.

****************************************************************************
*                               Qt 5.9.2 Changes                           *
****************************************************************************

- Don't crash when removing clip animators
- Case-insensitive matching of DDS/PKM files [QTBUG-61760]
- Fix crash when using a single key frame
- Fix rendering glitches with 2D text
- Respect render target selector when reading pixel data back [QTBUG-61547]
- Fix memory leaks when loading multiple scenes with assimp [QTBUG-61856]
- Fix updates of some render states
- Fix sorting of render commands [QTBUG-60183]
- Fix assert crash when updating 3D content of a Scene3D item [QTBUG-60613]
- Fix crash when loading multiple assimp scenes
- Fix shader compilation on Rasp-Pi [QTBUG-59349]
- Don't deref null VAOs [QTBUG-59349]
- Respect byteStride in QAttribute
- Don't crash when rapidly changing textures
- Ignore ill-formed faces in OBJ files
- Many performance and memory use reduction changes
