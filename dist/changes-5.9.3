Qt 5.9.3 is a bug-fix release. It maintains both forward and backward
compatibility (source and binary) with Qt 5.9.0.

For more details, refer to the online documentation included in this
distribution. The documentation is also available online:

http://doc.qt.io/qt-5/index.html

The Qt version 5.9 series is binary compatible with the 5.8.x series.
Applications compiled for 5.8 will continue to run with 5.9.

Some of the changes listed in this file include issue tracking numbers
corresponding to tasks in the Qt Bug Tracker:

https://bugreports.qt.io/

Each of these identifiers can be entered in the bug tracker to obtain more
information about a particular change.

****************************************************************************
*                               Qt 5.9.3 Changes                           *
****************************************************************************

 - Render next frame if texture data is not available [QTBUG-63561][QTBUG-59752]
 - Resource handling rework to reduce memory consumption
 - Fix SortPolicy sorting key generation
 - Minor code and documentation improvements.
 - Avoid sending certain notifications to the backend that aren't required
